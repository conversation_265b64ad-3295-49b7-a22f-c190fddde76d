import React from 'react';
import { useThemeStore } from '../../stores/themeStore';

export interface AppContentAreaProps {
  appIcon: React.ReactNode;
  appColor: string;
  viewTitle: string;
  viewContent: string;
  onGetStarted?: () => void;
  className?: string;
  'data-testid'?: string;
}

/**
 * AppContentArea Component
 * 
 * Displays the main content area with app icon, title, description, and action button.
 * Used within DynamicAppView to show the current view's content.
 * 
 * Features:
 * - Centered layout with app branding
 * - Themed styling using theme store
 * - Customizable action button
 * - Responsive design
 * - App-specific color theming
 */
const AppContentArea: React.FC<AppContentAreaProps> = ({
  appIcon,
  appColor,
  viewTitle,
  viewContent,
  onGetStarted = () => console.log('Get started clicked'),
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  return (
    <div
      className={`rounded-lg border p-6 ${className}`}
      style={{
        backgroundColor: colors.surface,
        borderColor: colors.border,
      }}
      data-testid={testId}
    >
      <div className="text-center py-12">
        <div
          className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center text-2xl"
          style={{ backgroundColor: appColor }}
        >
          {appIcon}
        </div>
        <h3
          className="text-lg font-semibold mb-2"
          style={{ color: colors.text }}
        >
          {viewTitle}
        </h3>
        <p
          className="text-sm max-w-md mx-auto"
          style={{ color: colors.textSecondary }}
        >
          {viewContent}
        </p>
        <div className="mt-6">
          <button
            className="px-4 py-2 rounded-lg text-white font-medium"
            style={{ backgroundColor: appColor }}
            onClick={onGetStarted}
          >
            Get Started
          </button>
        </div>
      </div>
    </div>
  );
};

export default AppContentArea;
