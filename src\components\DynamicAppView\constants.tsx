import React from 'react';
import { BellIcon, UserIcon } from '../icons';

/**
 * Mock user data for DynamicAppView
 * This would typically come from a user context or API in a real application
 */
export const mockUserData = {
  name: '<PERSON>',
  avatar: (
    <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white">
      <UserIcon className="w-5 h-5" />
    </div>
  ),
  notifications: [{ count: 3, icon: <BellIcon className="w-5 h-5" /> }],
};

/**
 * Default view data configuration for DynamicAppView
 * Provides consistent structure for view configuration
 */
export const createDefaultViewData = (currentViewTitle: string) => ({
  title: currentViewTitle,
  actions: [
    {
      label: 'New',
      onClick: () => console.log('New clicked'),
      isPrimary: true,
    },
    {
      label: 'Import',
      onClick: () => console.log('Import clicked'),
      isPrimary: false,
    },
  ],
  search: {
    // New CenteredSearchChipInput data
    filterTags: [{ id: 'active', label: 'Active Records', removable: true }],
    filterItems: [
      { id: 'active', label: 'Active', selected: true },
      { id: 'archived', label: 'Archived', selected: false },
      { id: 'draft', label: 'Draft', selected: false },
      { id: 'published', label: 'Published', selected: false },
      { id: 'date-range', label: 'Date Range', hasDropdown: true },
    ],
    groupByItems: [
      { id: 'status', label: 'Status' },
      { id: 'category', label: 'Category' },
      { id: 'created-date', label: 'Created Date', hasDropdown: true },
      { id: 'assigned-user', label: 'Assigned User' },
    ],
    favoriteItems: [
      { id: 'my-records', label: 'My Records', selected: false },
      { id: 'recent-activity', label: 'Recent Activity', selected: false },
      { id: 'high-priority', label: 'High Priority', selected: true },
      { id: 'pending-review', label: 'Pending Review', selected: false },
    ],
    onSearch: (query: string) => console.log('Search:', query),
    onTagRemove: (tagId: string) => console.log('Remove tag:', tagId),
    onFilterSelect: (filterId: string) =>
      console.log('Filter selected:', filterId),
    onGroupBySelect: (groupId: string) =>
      console.log('Group by selected:', groupId),
    onFavoriteSelect: (favoriteId: string) =>
      console.log('Favorite selected:', favoriteId),
    onFavoriteDelete: (favoriteId: string) =>
      console.log('Favorite deleted:', favoriteId),
    onAddCustomFilter: () => console.log('Add custom filter'),
    onAddCustomGroup: () => console.log('Add custom group'),
    onSaveCurrentSearch: () => console.log('Save current search'),
    // Legacy support for mobile search
    filters: [
      { id: 'active', label: 'Active' },
      { id: 'archived', label: 'Archived' },
    ],
    onRemoveFilter: (id: any) => console.log('Remove filter:', id),
  },
  pagination: {
    currentRange: '1-20 / 100',
    onNext: () => console.log('Next page'),
    onPrev: () => console.log('Previous page'),
  },
  viewModes: [
    { name: 'List', icon: '📋' },
    { name: 'Grid', icon: '⊞' },
    { name: 'Chart', icon: '📊' },
  ],
  activeViewMode: 'List',
});

/**
 * Creates app header data configuration
 */
export const createAppHeaderData = (
  appTitle: string,
  appIcon: React.ReactNode,
  appColor: string,
  navLinks: Array<{
    label: string;
    href: string;
    isActive: boolean;
  }>
) => ({
  name: appTitle,
  icon: (
    <div
      className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-lg"
      style={{ backgroundColor: appColor }}
    >
      {appIcon}
    </div>
  ),
  navLinks,
});
