import React from 'react';
import { useThemeStore } from '../../stores/themeStore';

export interface AppNotFoundStateProps {
  className?: string;
  'data-testid'?: string;
}

/**
 * AppNotFoundState Component
 * 
 * Displays a centered error state when an app is not found.
 * Used within DynamicAppView when no valid app data is available.
 * 
 * Features:
 * - Themed styling using theme store
 * - Centered layout with error message
 * - Accessible and responsive design
 */
const AppNotFoundState: React.FC<AppNotFoundStateProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  return (
    <div 
      className={`min-h-screen bg-background flex items-center justify-center ${className}`}
      data-testid={testId}
    >
      <div className="text-center">
        <h2
          className="text-xl font-semibold mb-2"
          style={{ color: colors.text }}
        >
          App not found
        </h2>
        <p className="text-sm" style={{ color: colors.textSecondary }}>
          Redirecting to dashboard...
        </p>
      </div>
    </div>
  );
};

export default AppNotFoundState;
