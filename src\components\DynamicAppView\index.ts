// Main component export
export { default as DynamicAppView } from './DynamicAppView';
export type { DynamicAppViewProps } from './DynamicAppView';

// Subcomponent exports (for potential reuse)
export { default as AppNotFoundState } from './AppNotFoundState';
export type { AppNotFoundStateProps } from './AppNotFoundState';

export { default as AppBreadcrumb } from './AppBreadcrumb';
export type { AppBreadcrumbProps } from './AppBreadcrumb';

export { default as AppContentArea } from './AppContentArea';
export type { AppContentAreaProps } from './AppContentArea';

// Constants and utilities
export {
  mockUserData,
  createDefaultViewData,
  createAppHeaderData
} from './constants';
