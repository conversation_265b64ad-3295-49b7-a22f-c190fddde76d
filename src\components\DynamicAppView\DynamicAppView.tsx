import React, { useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useThemeStore } from '../../stores/themeStore';
import DynamicAppHeader from '../layout/DynamicAppHeader';
import AppDynamicContent from '../layout/AppDynamicContent';
import { cn } from '../../utils/cn';
import { getAppById } from '../../data/mockApps';
// Extracted subcomponents
import AppNotFoundState from './AppNotFoundState';
import AppBreadcrumb from './AppBreadcrumb';
import AppContentArea from './AppContentArea';
import { mockUserData, createDefaultViewData, createAppHeaderData } from './constants';

export interface DynamicAppViewProps {
  className?: string;
  'data-testid'?: string;
}

const DynamicAppView: React.FC<DynamicAppViewProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { colors } = useThemeStore();

  const menuId = searchParams.get('menu');
  const viewId = searchParams.get('view') || 'dashboard';

  // Get app data based on menu ID
  const appData = menuId ? getAppById(menuId) : null;

  // Redirect to dashboard if no valid app is found
  useEffect(() => {
    if (!menuId || !appData) {
      navigate('/dashboard');
    }
  }, [menuId, appData, navigate]);

  if (!appData) {
    return <AppNotFoundState data-testid="app-not-found" />;
  }

  // Update nav links to show active state based on current view
  const updatedNavLinks = appData.navLinks
    ? appData.navLinks.map(link => ({
        ...link,
        isActive: link.href.includes(`view=${viewId}`),
      }))
    : [
        {
          label: 'Dashboard',
          href: `/app?menu=${menuId}&view=dashboard`,
          isActive: viewId === 'dashboard',
        },
      ];

  const currentView = appData.views
    ? appData.views[viewId as keyof typeof appData.views] ||
      appData.views.dashboard
    : {
        title: `${appData.title} Dashboard`,
        content: `Welcome to ${appData.title}. This is the main dashboard view.`,
      };

  // Create view data for the header using extracted function
  const viewData = createDefaultViewData(currentView.title);

  // Create app header data using extracted function
  const appHeaderData = createAppHeaderData(
    appData.title,
    appData.icon,
    appData.color,
    updatedNavLinks
  );

  return (
    <div
      className={cn('min-h-screen', className)}
      style={{ backgroundColor: colors.background }}
      data-testid={testId}
    >
      {/* Dynamic App Header - Top navigation only */}
      <DynamicAppHeader
        app={appHeaderData}
        user={mockUserData}
      />

      {/* App Dynamic Content - Bottom bar + main content */}
      <AppDynamicContent view={viewData}>
        <div className="space-y-6">
          {/* Breadcrumb Navigation */}
          <AppBreadcrumb
            appTitle={appData.title}
            currentViewTitle={currentView.title}
            data-testid="app-breadcrumb"
          />

          {/* Main Content Area */}
          <AppContentArea
            appIcon={appData.icon}
            appColor={appData.color}
            viewTitle={currentView.title}
            viewContent={currentView.content}
            data-testid="app-content-area"
          />
        </div>
      </AppDynamicContent>
    </div>
  );
};

export default DynamicAppView;
