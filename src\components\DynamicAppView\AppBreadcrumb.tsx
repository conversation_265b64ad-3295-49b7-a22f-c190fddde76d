import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useThemeStore } from '../../stores/themeStore';

export interface AppBreadcrumbProps {
  appTitle: string;
  currentViewTitle: string;
  className?: string;
  'data-testid'?: string;
}

/**
 * AppBreadcrumb Component
 * 
 * Displays a breadcrumb navigation showing the path from Dashboard > App > Current View.
 * Used within DynamicAppView to provide navigation context.
 * 
 * Features:
 * - Clickable dashboard link for navigation
 * - Themed styling using theme store
 * - Responsive text sizing
 * - Accessible navigation structure
 */
const AppBreadcrumb: React.FC<AppBreadcrumbProps> = ({
  appTitle,
  currentViewTitle,
  className = '',
  'data-testid': testId,
}) => {
  const navigate = useNavigate();
  const { colors } = useThemeStore();

  return (
    <nav 
      className={`flex items-center space-x-2 text-sm ${className}`}
      data-testid={testId}
    >
      <button
        onClick={() => navigate('/dashboard')}
        className="hover:underline"
        style={{ color: colors.textSecondary }}
      >
        Dashboard
      </button>
      <span style={{ color: colors.textSecondary }}>/</span>
      <span style={{ color: colors.text }}>{appTitle}</span>
      <span style={{ color: colors.textSecondary }}>/</span>
      <span style={{ color: colors.text }}>{currentViewTitle}</span>
    </nav>
  );
};

export default AppBreadcrumb;
