import type { Meta, StoryObj } from '@storybook/react-vite';
import { FilterDropdown } from './FilterDropdown';
import { useThemeStore } from '../../../stores/themeStore';
import { useEffect, useState } from 'react';

const meta: Meta<typeof FilterDropdown> = {
  title: 'UI/FilterDropdown',
  component: FilterDropdown,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A compact filter dropdown component with three sections: Filters, Group By, and Favorites.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    isOpen: {
      control: 'boolean',
      description: 'Whether the dropdown is open',
    },
    compact: {
      control: 'boolean',
      description: 'Whether to use compact styling',
    },
    searchQuery: {
      control: 'text',
      description: 'Search query to filter items',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const sampleFilterItems = [
  { id: 'my-quotations', label: 'My Quotations', selected: true },
  { id: 'quotations', label: 'Quotations', selected: false },
  { id: 'sales-orders', label: 'Sales Orders', selected: false },
  { id: 'create-date', label: 'Create Date', hasDropdown: true },
];

const sampleGroupByItems = [
  { id: 'salesperson', label: 'Salesperson' },
  { id: 'customer', label: 'Customer' },
  { id: 'order-date', label: 'Order Date', hasDropdown: true },
];

const sampleFavoriteItems = [
  { id: 'fully-invoiced', label: 'Fully Invoiced', selected: true },
  { id: 'quotations', label: 'Quotations', selected: false },
  { id: 'undelivered-complete', label: 'Undelivered Complete', selected: false },
  { id: 'unpaid-orders', label: 'Unpaid Orders', selected: false },
];

const Template = (args: any) => {
  const { colors, setTheme } = useThemeStore();
  const [filterItems, setFilterItems] = useState(args.filterItems || []);
  const [favoriteItems, setFavoriteItems] = useState(args.favoriteItems || []);

  useEffect(() => {
    setTheme('dark');
  }, [setTheme]);

  const handleFilterSelect = (filterId: string) => {
    setFilterItems((prev: any) =>
      prev.map((item: any) =>
        item.id === filterId ? { ...item, selected: !item.selected } : item
      )
    );
  };

  const handleFavoriteSelect = (favoriteId: string) => {
    setFavoriteItems((prev: any) =>
      prev.map((item: any) =>
        item.id === favoriteId ? { ...item, selected: !item.selected } : item
      )
    );
  };

  const handleFavoriteDelete = (favoriteId: string) => {
    setFavoriteItems((prev: any) => prev.filter((item: any) => item.id !== favoriteId));
  };

  return (
    <div
      className="p-8 min-h-screen relative"
      style={{ backgroundColor: colors.background }}
    >
      <div className="relative inline-block">
        <div
          className="w-96 h-8 border rounded px-3 flex items-center"
          style={{
            backgroundColor: colors.surface,
            borderColor: colors.border,
            color: colors.text,
          }}
        >
          Search input placeholder...
        </div>
        
        <FilterDropdown
          {...args}
          filterItems={filterItems}
          favoriteItems={favoriteItems}
          onFilterSelect={handleFilterSelect}
          onFavoriteSelect={handleFavoriteSelect}
          onFavoriteDelete={handleFavoriteDelete}
          onGroupBySelect={(id) => console.log('Group by:', id)}
          onAddCustomFilter={() => console.log('Add custom filter')}
          onAddCustomGroup={() => console.log('Add custom group')}
          onSaveCurrentSearch={() => console.log('Save current search')}
        />
      </div>
    </div>
  );
};

export const Default: Story = {
  render: Template,
  args: {
    isOpen: true,
    filterItems: sampleFilterItems,
    groupByItems: sampleGroupByItems,
    favoriteItems: sampleFavoriteItems,
    compact: false,
  },
};

export const Compact: Story = {
  render: Template,
  args: {
    isOpen: true,
    filterItems: sampleFilterItems,
    groupByItems: sampleGroupByItems,
    favoriteItems: sampleFavoriteItems,
    compact: true,
  },
};

export const WithSearch: Story = {
  render: Template,
  args: {
    isOpen: true,
    filterItems: sampleFilterItems,
    groupByItems: sampleGroupByItems,
    favoriteItems: sampleFavoriteItems,
    searchQuery: 'quot',
    compact: true,
  },
};

export const Closed: Story = {
  render: Template,
  args: {
    isOpen: false,
    filterItems: sampleFilterItems,
    groupByItems: sampleGroupByItems,
    favoriteItems: sampleFavoriteItems,
  },
};

export const EmptyState: Story = {
  render: Template,
  args: {
    isOpen: true,
    filterItems: [],
    groupByItems: [],
    favoriteItems: [],
    compact: true,
  },
};
